import './styles.css';

// Import images
import portraitImg from '../assets/portrait.jpg';
import githubIcon from '../assets/icon_github.png';
import instagramIcon from '../assets/icon_instagram.png';
import facebookIcon from '../assets/icon_facebook.png';

// DOM elements
const hamburger = document.querySelector('.hamburger') as HTMLElement;
const navMenu = document.querySelector('.nav-menu') as HTMLElement;
const navLinks = document.querySelectorAll('.nav-link');
const navbar = document.querySelector('.navbar') as HTMLElement;

// Mobile menu toggle
function toggleMobileMenu(): void {
    hamburger?.classList.toggle('active');
    navMenu?.classList.toggle('active');
}

// Close mobile menu when clicking on a link
function closeMobileMenu(): void {
    hamburger?.classList.remove('active');
    navMenu?.classList.remove('active');
}

// Navbar scroll effect
function handleNavbarScroll(): void {
    if (window.scrollY > 50) {
        navbar?.classList.add('scrolled');
    } else {
        navbar?.classList.remove('scrolled');
    }
}

// Smooth scrolling for anchor links
function handleSmoothScroll(e: Event): void {
    const target = e.target as HTMLAnchorElement;
    const href = target.getAttribute('href');
    
    if (href && href.startsWith('#')) {
        e.preventDefault();
        const targetElement = document.querySelector(href);
        
        if (targetElement) {
            const offsetTop = (targetElement as HTMLElement).offsetTop - 70; // Account for fixed navbar
            window.scrollTo({
                top: offsetTop,
                behavior: 'smooth'
            });
        }
        
        // Close mobile menu if open
        closeMobileMenu();
    }
}

// Intersection Observer for animations
function setupScrollAnimations(): void {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.about-text, .about-skills, .contact-content, .skill-item');
    animateElements.forEach(el => observer.observe(el));
}

// Typing animation for hero title
function typeWriter(element: HTMLElement, text: string, speed: number = 100): void {
    let i = 0;
    element.innerHTML = '';
    
    function type() {
        if (i < text.length) {
            element.innerHTML += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }
    
    type();
}

// Simple typing animation that preserves HTML
function typeWriterWithHTML(element: HTMLElement, speed: number = 100): void {
    const originalHTML = element.innerHTML;
    element.innerHTML = '';
    
    // Extract text content and HTML structure
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = originalHTML;
    const textContent = tempDiv.textContent || '';
    
    let i = 0;
    function type() {
        if (i < textContent.length) {
            // Reconstruct the HTML with the current text
            const currentText = textContent.substring(0, i + 1);
            element.innerHTML = originalHTML.replace(/吳政霖/g, currentText);
            i++;
            setTimeout(type, speed);
        } else {
            // Ensure final result is correct
            element.innerHTML = originalHTML;
        }
    }
    
    type();
}

// Initialize typing animation
function initTypingAnimation(): void {
    const heroTitle = document.querySelector('.hero-title') as HTMLElement;
    if (heroTitle) {
        // Set the correct HTML content directly
        heroTitle.innerHTML = '你好，我是 <span class="highlight">吳政霖</span>';
    }
}

// Parallax effect for hero section
function handleParallax(): void {
    const hero = document.querySelector('.hero') as HTMLElement;
    if (hero) {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;
        hero.style.transform = `translateY(${rate}px)`;
    }
}

// Social link click tracking
function trackSocialClick(platform: string): void {
    console.log(`Social link clicked: ${platform}`);
    // You can add analytics tracking here
}

// Add click handlers to social links
function setupSocialTracking(): void {
    const socialLinks = document.querySelectorAll('.social-link');
    socialLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            const target = e.target as HTMLElement;
            const platform = target.closest('.social-link')?.querySelector('span')?.textContent || 'Unknown';
            trackSocialClick(platform);
        });
    });
}

// Skill item hover effects
function setupSkillHoverEffects(): void {
    const skillItems = document.querySelectorAll('.skill-item');
    skillItems.forEach(item => {
        item.addEventListener('mouseenter', () => {
            (item as HTMLElement).style.transform = 'translateY(-5px) scale(1.02)';
        });
        
        item.addEventListener('mouseleave', () => {
            (item as HTMLElement).style.transform = 'translateY(0) scale(1)';
        });
    });
}

// Navbar active link highlighting
function updateActiveNavLink(): void {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    window.addEventListener('scroll', () => {
        let current = '';
        
        sections.forEach(section => {
            const sectionTop = (section as HTMLElement).offsetTop - 100;
            const sectionHeight = (section as HTMLElement).offsetHeight;
            
            if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
                current = section.getAttribute('id') || '';
            }
        });
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${current}`) {
                link.classList.add('active');
            }
        });
    });
}

// Set up images
function setupImages(): void {
    // Set portrait image
    const portrait = document.querySelector('.portrait') as HTMLImageElement;
    if (portrait) {
        portrait.src = portraitImg;
    }
    
    // Set social media icons
    const githubImg = document.querySelector('.social-link:nth-child(1) img') as HTMLImageElement;
    if (githubImg) {
        githubImg.src = githubIcon;
    }
    
    const instagramImg = document.querySelector('.social-link:nth-child(2) img') as HTMLImageElement;
    if (instagramImg) {
        instagramImg.src = instagramIcon;
    }
    
    const facebookImg = document.querySelector('.social-link:nth-child(3) img') as HTMLImageElement;
    if (facebookImg) {
        facebookImg.src = facebookIcon;
    }
}

// Initialize all functionality
function init(): void {
    // Event listeners
    hamburger?.addEventListener('click', toggleMobileMenu);
    
    navLinks.forEach(link => {
        link.addEventListener('click', handleSmoothScroll);
    });
    
    window.addEventListener('scroll', () => {
        handleNavbarScroll();
        handleParallax();
    });
    
    // Initialize features
    setupImages();
    setupScrollAnimations();
    setupSocialTracking();
    setupSkillHoverEffects();
    updateActiveNavLink();
    
    // Initialize animations after a short delay
    setTimeout(() => {
        initTypingAnimation();
    }, 1000);
    
    console.log('Personal site initialized successfully!');
}

// Wait for DOM to be fully loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
} else {
    init();
}

// Export for potential module usage
export { init, toggleMobileMenu, closeMobileMenu };
