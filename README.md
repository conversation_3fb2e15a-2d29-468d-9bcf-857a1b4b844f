# 吳政霖 - Personal Site

一個現代化的個人網站，使用 HTML、CSS 和 TypeScript 建構，採用響應式設計。

## 功能特色

- 🎨 現代化設計風格
- 📱 完全響應式設計
- ⚡ 流暢的動畫效果
- 🎯 平滑滾動導航
- 📱 行動裝置友善的選單
- 🔗 社交媒體連結整合

## 技術棧

- **HTML5** - 語義化標記
- **CSS3** - 現代化樣式與動畫
- **TypeScript** - 型別安全的 JavaScript
- **Webpack** - 模組打包工具
- **Yarn** - 套件管理

## 專案結構

```
personal-site/
├── public/
│   └── index.html          # HTML 模板
├── src/
│   ├── index.ts            # TypeScript 主檔案
│   └── styles.css          # CSS 樣式
├── assets/                 # 圖片資源
├── dist/                   # 建置輸出
├── package.json            # 專案配置
├── tsconfig.json           # TypeScript 配置
├── webpack.config.js       # Webpack 配置
└── README.md              # 專案說明
```

## 安裝與執行

### 前置需求

- Node.js (建議版本 16+)
- Yarn

### 安裝依賴

```bash
yarn install
```

### 開發模式

```bash
yarn dev
```

開發伺服器將在 http://localhost:3000 啟動

### 建置生產版本

```bash
yarn build
```

建置檔案將輸出到 `dist/` 目錄

## 功能說明

### 導航功能
- 固定頂部導航列
- 平滑滾動到對應區塊
- 行動裝置漢堡選單
- 當前區塊高亮顯示

### 首頁區塊
- 個人介紹與照片
- 動態打字效果
- 視差滾動效果
- 行動呼籲按鈕

### 關於我區塊
- 個人背景介紹
- 技能專長展示
- 興趣愛好列表
- 滾動動畫效果

### 聯絡我區塊
- 社交媒體連結
- 圖示按鈕設計
- 懸停動畫效果

## 自訂說明

### 修改個人資訊
編輯 `public/index.html` 中的內容：
- 姓名與標題
- 個人介紹文字
- 技能專長
- 社交媒體連結

### 更換圖片
將新圖片放入 `assets/` 目錄，並更新 HTML 中的圖片路徑。

### 修改樣式
編輯 `src/styles.css` 來自訂：
- 顏色主題
- 字體設定
- 動畫效果
- 響應式斷點

## 瀏覽器支援

- Chrome (最新版本)
- Firefox (最新版本)
- Safari (最新版本)
- Edge (最新版本)

## 授權

MIT License

## 聯絡資訊

- GitHub: [johnny20tech](https://github.com/johnny20tech)
- Instagram: [@1030_wzl](https://www.instagram.com/1030_wzl/)
- Facebook: [johnny.wu.71216](https://www.facebook.com/johnny.wu.71216)
