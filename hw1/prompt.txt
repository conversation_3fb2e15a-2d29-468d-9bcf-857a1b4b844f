幫我用 HTML, CSS, TypeScript 做一個純前端的 personal site, 目錄名稱就叫 "personal-site-v2"，路徑為"./hw1/personal-site-v2"

- 用 yarn 管理套件與專案
- 參考 https://stanleyshen2003.github.io/me 的風格來製作網頁，若連結不到網站可參考 ./img/style.png
- 根據 intro.txt 生成 about 頁面
- intro.txt 內給的連結要有按鈕可以點選前往，且按鈕的圖片請用 ./img 資料夾中的對應 icon 圖片
- 頁面中的個人照請用 ./img/portrait.jpg，左上角的圖像請選用 ./img/avatar_1.png or ./img/avatar_2.jpg
- 請加上 git 的管理
- 請撰寫 README.md 在 hw1 資料夾中，說明如何安裝套件及開啟網站